package inks.oms.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

/**
 * 数据库初始化配置
 * 实现ApplicationContextInitializer接口，确保在Spring容器初始化前执行数据库初始化
 * 这样可以在HikariPool等数据源组件初始化之前完成数据库的创建和初始化
 */
public class DatabaseInitializationConfiguration implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseInitializationConfiguration.class);

    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        Environment env = applicationContext.getEnvironment();

        logger.info("Starting database pre-initialization...");

        // 创建数据库初始化服务实例
        DatabaseInitializationService databaseInitializationService = new DatabaseInitializationService();

        try {
            // 初始化 inkssaas 数据库
            String inkssaasUrl = env.getProperty("spring.datasource.dynamic.datasource.inkssaas.url");
            String inkssaasUsername = env.getProperty("spring.datasource.dynamic.datasource.inkssaas.username");
            String inkssaasPassword = env.getProperty("spring.datasource.dynamic.datasource.inkssaas.password");
            String inkssaasSqlUrl = env.getProperty("initialization.sql.inkssaas-url");

            if (databaseInitializationService.validateConfiguration("inkssaas", inkssaasUrl, inkssaasUsername, inkssaasPassword, inkssaasSqlUrl)) {
                databaseInitializationService.initializeDatabase(
                        "inkssaas",
                        inkssaasUrl,
                        inkssaasUsername,
                        inkssaasPassword,
                        inkssaasSqlUrl
                );
            } else {
                logger.warn("Inkssaas 数据库配置不完整，跳过初始化");
            }

            // 初始化 flowable 数据库
            String flowableUrl = env.getProperty("spring.datasource.dynamic.datasource.flowable.url");
            String flowableUsername = env.getProperty("spring.datasource.dynamic.datasource.flowable.username");
            String flowablePassword = env.getProperty("spring.datasource.dynamic.datasource.flowable.password");
            String flowableSqlUrl = env.getProperty("initialization.sql.flowable-url");

            if (databaseInitializationService.validateConfiguration("flowable", flowableUrl, flowableUsername, flowablePassword, flowableSqlUrl)) {
                databaseInitializationService.initializeDatabase(
                        "flowable",
                        flowableUrl,
                        flowableUsername,
                        flowablePassword,
                        flowableSqlUrl
                );
            } else {
                logger.warn("Flowable 数据库配置不完整，跳过初始化");
            }

            logger.info("数据库预初始化完成");
        } catch (Exception e) {
            logger.error("数据库预初始化失败", e);
            // 抛出异常以阻止应用启动
            throw new RuntimeException("数据库预初始化失败", e);
        }
    }
}
