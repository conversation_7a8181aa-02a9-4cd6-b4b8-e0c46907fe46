package inks.oms.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.jdbc.datasource.init.ScriptUtils;
import org.springframework.core.io.ByteArrayResource;

import java.sql.*;
import java.util.Arrays;

/**
 * 数据库初始化服务
 * 负责创建数据库和执行SQL脚本
 */
public class DatabaseInitializationService {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseInitializationService.class);
    private final RestTemplate restTemplate;

    public DatabaseInitializationService() {
        this.restTemplate = new RestTemplate();
        // 设置HTTP请求超时
        this.restTemplate.getRequestFactory();
    }

    /**
     * 验证数据库配置的完整性
     */
    public boolean validateConfiguration(String dbType, String jdbcUrl, String username, String password, String sqlScriptUrl) {
        logger.info("验证 {} 数据库配置...", dbType);

        if (jdbcUrl == null || jdbcUrl.trim().isEmpty()) {
            logger.error("{} 数据库URL未配置", dbType);
            return false;
        }

        if (username == null || username.trim().isEmpty()) {
            logger.error("{} 数据库用户名未配置", dbType);
            return false;
        }

        if (password == null) {
            logger.error("{} 数据库密码未配置", dbType);
            return false;
        }

        if (sqlScriptUrl == null || sqlScriptUrl.trim().isEmpty()) {
            logger.warn("{} SQL脚本URL未配置，将跳过脚本执行", dbType);
        }

        // 验证JDBC URL格式
        String dbName = extractDatabaseName(jdbcUrl);
        if (dbName == null) {
            logger.error("{} 数据库URL格式无效: {}", dbType, jdbcUrl);
            return false;
        }

        logger.info("{} 数据库配置验证通过，目标数据库: {}", dbType, dbName);
        return true;
    }

    /**
     * 初始化数据库（创建数据库并执行SQL脚本）
     * @param dbType 数据库类型标识
     * @param jdbcUrl 数据库连接URL
     * @param username 用户名
     * @param password 密码
     * @param sqlScriptUrl SQL脚本的HTTP地址
     */
    public void initializeDatabase(String dbType, String jdbcUrl, String username, String password, String sqlScriptUrl) {
        if (jdbcUrl == null || username == null || password == null) {
            logger.warn("Database configuration for '{}' is not fully provided. Skipping initialization.", dbType);
            return;
        }

        String dbName = extractDatabaseName(jdbcUrl);
        if (dbName == null) {
            logger.error("Could not extract database name from URL: {}. Skipping initialization for {}.", jdbcUrl, dbType);
            return;
        }

        try {
            logger.info("Starting database initialization for: {}", dbType);

            // 1. Create database if not exists
            createDatabaseIfNotExists(dbName, jdbcUrl, username, password);

            // 2. Check if database is already initialized
            if (isDatabaseInitialized(jdbcUrl, username, password, dbName)) {
                logger.info("Database '{}' is already initialized, skipping SQL script execution.", dbType);
                return;
            }

            // 3. Execute SQL script from URL
            if (sqlScriptUrl != null && !sqlScriptUrl.trim().isEmpty()) {
                executeSqlFromUrl(jdbcUrl, username, password, sqlScriptUrl);
            } else {
                logger.warn("No SQL script URL provided for database: {}", dbType);
            }

            logger.info("Database initialization completed for: {}", dbType);

        } catch (Exception e) {
            logger.error("Failed to initialize database: {}", dbType, e);
            // 抛出运行时异常以阻止应用启动
            throw new RuntimeException("Database initialization failed for " + dbType, e);
        }
    }

    /**
     * 从JDBC URL中提取数据库名称
     */
    private String extractDatabaseName(String jdbcUrl) {
        try {
            logger.debug("正在解析数据库名称，URL: {}", jdbcUrl);

            // 使用正则表达式匹配标准的JDBC URL格式
            // **********************/database?params
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("jdbc:mysql://[^/]+:(\\d+)/([^?]+)");
            java.util.regex.Matcher matcher = pattern.matcher(jdbcUrl);

            if (matcher.find()) {
                String dbName = matcher.group(2);
                logger.debug("解析出的数据库名称: {}", dbName);
                return dbName.trim();
            } else {
                logger.error("URL格式不匹配标准JDBC格式: {}", jdbcUrl);
                return null;
            }

        } catch (Exception e) {
            logger.error("解析数据库名称失败，URL: {}", jdbcUrl, e);
        }
        return null;
    }

    /**
     * 创建数据库（如果不存在）
     */
    private void createDatabaseIfNotExists(String dbName, String jdbcUrl, String username, String password) throws SQLException {
        // 构建服务器连接URL（不包含数据库名）
        String serverUrl = buildServerUrl(jdbcUrl);
        logger.info("正在连接MySQL服务器: {}", serverUrl.replaceAll("password=[^&]*", "password=***"));

        // Ensure MySQL driver is loaded
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            logger.error("MySQL JDBC Driver not found.", e);
            throw new SQLException("MySQL JDBC Driver not found.", e);
        }

        try (Connection connection = DriverManager.getConnection(serverUrl, username, password);
             Statement statement = connection.createStatement()) {

            String createDbSql = "CREATE DATABASE IF NOT EXISTS `" + dbName + "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            logger.info("执行SQL: {}", createDbSql);
            statement.executeUpdate(createDbSql);
            logger.info("数据库 '{}' 创建成功或已存在", dbName);

        } catch (SQLException e) {
            logger.error("创建数据库 {} 失败", dbName, e);
            throw e;
        }
    }

    /**
     * 构建MySQL服务器连接URL（不包含数据库名）
     */
    private String buildServerUrl(String jdbcUrl) {
        try {
            // 使用正则表达式提取主机和端口部分
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(jdbc:mysql://[^/]+:\\d+)/[^?]+(.*)");
            java.util.regex.Matcher matcher = pattern.matcher(jdbcUrl);

            if (matcher.find()) {
                String hostPort = matcher.group(1);  // **********************
                String params = matcher.group(2);    // ?params 或空字符串

                // 确保包含必要的参数
                if (params.isEmpty()) {
                    params = "?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&useUnicode=true&characterEncoding=utf-8";
                } else {
                    // 检查并添加缺失的参数
                    if (!params.contains("useSSL=")) {
                        params += "&useSSL=false";
                    }
                    if (!params.contains("allowPublicKeyRetrieval=")) {
                        params += "&allowPublicKeyRetrieval=true";
                    }
                }

                String serverUrl = hostPort + params;
                logger.debug("构建的服务器URL: {}", serverUrl);
                return serverUrl;
            } else {
                throw new IllegalArgumentException("无法解析JDBC URL格式: " + jdbcUrl);
            }

        } catch (Exception e) {
            logger.error("构建服务器URL失败: {}", jdbcUrl, e);
            throw new IllegalArgumentException("Failed to build server URL from: " + jdbcUrl, e);
        }
    }

    /**
     * 检查数据库是否已经初始化（通过检查是否存在表）
     */
    private boolean isDatabaseInitialized(String jdbcUrl, String username, String password, String dbName) {
        try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password);
             Statement statement = connection.createStatement()) {

            // 检查数据库中是否存在表
            String checkTablesSql = "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = '" + dbName + "'";
            ResultSet resultSet = statement.executeQuery(checkTablesSql);
            
            if (resultSet.next()) {
                int tableCount = resultSet.getInt("table_count");
                logger.info("数据库 '{}' 当前有 {} 张数据表", dbName, tableCount);
                return tableCount > 0; // 如果有表存在，认为已经初始化
            }
            
        } catch (SQLException e) {
            logger.warn("Could not check if database '{}' is initialized: {}", dbName, e.getMessage());
            return false; // 如果检查失败，假设未初始化
        }
        return false;
    }

    /**
     * 从URL下载并执行SQL脚本
     */
    private void executeSqlFromUrl(String jdbcUrl, String username, String password, String sqlScriptUrl) throws SQLException {
        logger.info("正在从远程地址下载SQL脚本: {}", sqlScriptUrl);

        try {
            // 添加超时配置
            restTemplate.getMessageConverters().clear();
            restTemplate.getMessageConverters().add(new org.springframework.http.converter.StringHttpMessageConverter(java.nio.charset.StandardCharsets.UTF_8));

            ResponseEntity<String> response = restTemplate.getForEntity(sqlScriptUrl, String.class);

            if (!response.getStatusCode().is2xxSuccessful() || response.getBody() == null) {
                throw new RuntimeException("下载SQL脚本失败，HTTP状态: " + response.getStatusCode() + ", URL: " + sqlScriptUrl);
            }

            String sqlScript = response.getBody().trim();
            if (sqlScript.isEmpty()) {
                logger.warn("下载的SQL脚本为空: {}", sqlScriptUrl);
                return;
            }

            logger.info("SQL脚本下载成功 (共 {} 字符)", sqlScript.length());
            executeSqlScript(jdbcUrl, username, password, sqlScript);

        } catch (Exception e) {
            logger.error("下载或执行SQL脚本失败: {}", sqlScriptUrl, e);
            throw new SQLException("执行远程SQL脚本失败: " + sqlScriptUrl, e);
        }
    }

    /**
     * 高性能SQL脚本执行 - 模拟Navicat的执行策略
     */
    private void executeSqlScript(String jdbcUrl, String username, String password, String sqlScript) throws SQLException {
        try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password)) {

            logger.info("开始执行SQL脚本 (高性能模式)，脚本大小: {} 字符", sqlScript.length());

            // 优化连接参数
            connection.setAutoCommit(false);

            try (Statement statement = connection.createStatement()) {

                // 设置批量执行参数
                statement.setFetchSize(1000);

                // 智能分割SQL语句
                String[] statements = fastSplitSql(sqlScript);

                int totalStatements = statements.length;
                logger.info("开始批量执行SQL脚本，共 {} 条语句", totalStatements);

                // 分类语句：DDL语句单线程，INSERT语句可以多线程
                java.util.List<String> ddlStatements = new java.util.ArrayList<>();
                java.util.List<String> insertStatements = new java.util.ArrayList<>();

                for (String sql : statements) {
                    String upperSql = sql.trim().toUpperCase();
                    if (upperSql.startsWith("CREATE") || upperSql.startsWith("DROP") ||
                        upperSql.startsWith("ALTER") || upperSql.startsWith("SET")) {
                        ddlStatements.add(sql);
                    } else if (upperSql.startsWith("INSERT")) {
                        insertStatements.add(sql);
                    } else {
                        ddlStatements.add(sql); // 其他语句保守处理
                    }
                }

                logger.info("语句分类: DDL {} 条, INSERT {} 条", ddlStatements.size(), insertStatements.size());

                long startTime = System.currentTimeMillis();
                int executedCount = 0;
                int failedCount = 0;

                // 第一阶段：单线程执行DDL语句
                logger.info("第一阶段：执行DDL语句...");
                for (String sql : ddlStatements) {
                    try {
                        statement.execute(sql);
                        executedCount++;
                    } catch (SQLException e) {
                        failedCount++;
                        if (failedCount <= 5) {
                            logger.warn("DDL执行失败 [{}]: {}", failedCount,
                                sql.substring(0, Math.min(sql.length(), 50)) + "...");
                        }
                    }
                }
                connection.commit();
                logger.info("DDL语句执行完成: 成功 {} 条, 失败 {} 条", executedCount, failedCount);

                // 第二阶段：批量执行INSERT语句
                if (!insertStatements.isEmpty()) {
                    logger.info("第二阶段：批量执行INSERT语句...");
                    int insertExecuted = executeBatchInserts(connection, insertStatements, startTime, totalStatements);
                    executedCount += insertExecuted;
                }

                // 最终提交
                connection.commit();

                long totalTime = System.currentTimeMillis() - startTime;
                logger.info("SQL脚本执行完成: 成功 {} 条, 失败 {} 条, 总耗时: {:.1f} 秒",
                    executedCount, failedCount, totalTime / 1000.0);

            } catch (SQLException e) {
                connection.rollback();
                throw e;
            }
        }
    }

    /**
     * 批量执行INSERT语句
     */
    private int executeBatchInserts(Connection connection, java.util.List<String> insertStatements,
                                   long startTime, int totalStatements) throws SQLException {
        int executedCount = 0;
        int failedCount = 0;
        int batchSize = 500; // INSERT语句批量大小
        int progressInterval = Math.max(1, insertStatements.size() / 20);

        try (Statement statement = connection.createStatement()) {

            for (int i = 0; i < insertStatements.size(); i++) {
                String sql = insertStatements.get(i);
                try {
                    statement.addBatch(sql);

                    // 每batchSize条执行一次批量操作
                    if ((i + 1) % batchSize == 0 || i == insertStatements.size() - 1) {
                        int[] results = statement.executeBatch();
                        for (int result : results) {
                            if (result >= 0) {
                                executedCount++;
                            } else {
                                failedCount++;
                            }
                        }
                        statement.clearBatch();
                        connection.commit();
                    }

                    // 显示进度
                    if ((i + 1) % progressInterval == 0 || i == insertStatements.size() - 1) {
                        long elapsed = System.currentTimeMillis() - startTime;
                        int progress = (int) ((double) (i + 1) / insertStatements.size() * 100);
                        double speed = (double) (i + 1) / elapsed * 1000;
                        logger.info("INSERT进度: {}/{}  ({}%) - 成功: {}, 失败: {}, 速度: {:.1f} 语句/秒",
                            i + 1, insertStatements.size(), progress, executedCount, failedCount, speed);
                    }

                } catch (SQLException e) {
                    failedCount++;
                    if (failedCount <= 5) {
                        logger.warn("INSERT执行失败 [{}]: {}", failedCount,
                            sql.substring(0, Math.min(sql.length(), 50)) + "...");
                    }
                }
            }
        }

        logger.info("INSERT语句执行完成: 成功 {} 条, 失败 {} 条", executedCount, failedCount);
        return executedCount;
    }

    /**
     * 智能SQL分割 - 处理字符串中的分号
     */
    private String[] fastSplitSql(String sqlScript) {
        // 移除多行注释
        sqlScript = sqlScript.replaceAll("/\\*[\\s\\S]*?\\*/", "");

        java.util.List<String> statements = new java.util.ArrayList<>();
        StringBuilder currentStatement = new StringBuilder();
        boolean inString = false;
        char stringChar = 0;

        for (int i = 0; i < sqlScript.length(); i++) {
            char c = sqlScript.charAt(i);

            if (!inString) {
                if (c == '\'' || c == '"') {
                    inString = true;
                    stringChar = c;
                    currentStatement.append(c);
                } else if (c == ';') {
                    // 语句结束
                    String stmt = currentStatement.toString().trim();
                    if (!stmt.isEmpty() && !isComment(stmt)) {
                        statements.add(stmt);
                    }
                    currentStatement = new StringBuilder();
                } else {
                    currentStatement.append(c);
                }
            } else {
                currentStatement.append(c);
                if (c == stringChar) {
                    // 检查是否是转义的引号
                    if (i + 1 < sqlScript.length() && sqlScript.charAt(i + 1) == stringChar) {
                        // 转义的引号，继续
                        currentStatement.append(sqlScript.charAt(i + 1));
                        i++; // 跳过下一个字符
                    } else {
                        // 字符串结束
                        inString = false;
                    }
                }
            }
        }

        // 添加最后一个语句
        String lastStmt = currentStatement.toString().trim();
        if (!lastStmt.isEmpty() && !isComment(lastStmt)) {
            statements.add(lastStmt);
        }

        return statements.toArray(new String[0]);
    }

    /**
     * 快速判断是否为注释
     */
    private boolean isComment(String sql) {
        return sql.startsWith("--") || sql.startsWith("/*") || sql.startsWith("*");
    }
}
