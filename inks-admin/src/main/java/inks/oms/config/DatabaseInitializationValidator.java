package inks.oms.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * 数据库初始化验证工具
 * 用于验证数据库连接和HTTP脚本可访问性
 */
public class DatabaseInitializationValidator {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseInitializationValidator.class);
    private final RestTemplate restTemplate;

    public DatabaseInitializationValidator() {
        this.restTemplate = new RestTemplate();
    }

    /**
     * 验证数据库服务器连接
     */
    public boolean validateDatabaseConnection(String jdbcUrl, String username, String password) {
        try {
            // 构建服务器连接URL
            String serverUrl = buildServerUrl(jdbcUrl);
            logger.info("测试数据库服务器连接: {}", serverUrl.replaceAll("password=[^&]*", "password=***"));

            // 加载MySQL驱动
            Class.forName("com.mysql.cj.jdbc.Driver");

            // 测试连接
            try (Connection connection = DriverManager.getConnection(serverUrl, username, password);
                 Statement statement = connection.createStatement()) {
                
                // 执行简单查询验证连接
                statement.executeQuery("SELECT 1");
                logger.info("数据库服务器连接成功");
                return true;
            }

        } catch (Exception e) {
            logger.error("数据库服务器连接失败", e);
            return false;
        }
    }

    /**
     * 验证HTTP脚本可访问性
     */
    public boolean validateHttpScript(String sqlScriptUrl) {
        if (sqlScriptUrl == null || sqlScriptUrl.trim().isEmpty()) {
            logger.warn("SQL脚本URL为空");
            return false;
        }

        try {
            logger.info("测试HTTP脚本访问: {}", sqlScriptUrl);
            
            // 发送HEAD请求检查资源是否存在
            restTemplate.headForHeaders(sqlScriptUrl);
            logger.info("HTTP脚本访问成功");
            return true;

        } catch (Exception e) {
            logger.error("HTTP脚本访问失败: {}", sqlScriptUrl, e);
            return false;
        }
    }

    /**
     * 验证数据库是否存在
     */
    public boolean validateDatabaseExists(String jdbcUrl, String username, String password) {
        try {
            logger.info("检查数据库是否存在: {}", extractDatabaseName(jdbcUrl));
            
            try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password);
                 Statement statement = connection.createStatement()) {
                
                statement.executeQuery("SELECT 1");
                logger.info("数据库已存在且可访问");
                return true;
            }

        } catch (SQLException e) {
            if (e.getMessage().contains("Unknown database")) {
                logger.info("数据库不存在，需要创建");
                return false;
            } else {
                logger.error("数据库连接检查失败", e);
                return false;
            }
        }
    }

    /**
     * 完整验证流程
     */
    public ValidationResult performFullValidation(String dbType, String jdbcUrl, String username, String password, String sqlScriptUrl) {
        logger.info("开始完整验证: {}", dbType);
        
        ValidationResult result = new ValidationResult();
        result.dbType = dbType;
        
        // 1. 验证服务器连接
        result.serverConnectionValid = validateDatabaseConnection(jdbcUrl, username, password);
        
        // 2. 验证数据库存在性
        if (result.serverConnectionValid) {
            result.databaseExists = validateDatabaseExists(jdbcUrl, username, password);
        }
        
        // 3. 验证HTTP脚本
        result.httpScriptValid = validateHttpScript(sqlScriptUrl);
        
        // 输出验证结果
        logger.info("验证结果 [{}]: 服务器连接={}, 数据库存在={}, HTTP脚本={}", 
                dbType, result.serverConnectionValid, result.databaseExists, result.httpScriptValid);
        
        return result;
    }

    /**
     * 构建服务器连接URL
     */
    private String buildServerUrl(String jdbcUrl) {
        try {
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(jdbc:mysql://[^/]+:\\d+)/[^?]+(.*)");
            java.util.regex.Matcher matcher = pattern.matcher(jdbcUrl);

            if (matcher.find()) {
                String hostPort = matcher.group(1);
                String params = matcher.group(2);

                if (params.isEmpty()) {
                    params = "?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&useUnicode=true&characterEncoding=utf-8";
                } else {
                    if (!params.contains("useSSL=")) {
                        params += "&useSSL=false";
                    }
                    if (!params.contains("allowPublicKeyRetrieval=")) {
                        params += "&allowPublicKeyRetrieval=true";
                    }
                }

                return hostPort + params;
            } else {
                throw new IllegalArgumentException("无法解析JDBC URL格式: " + jdbcUrl);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to build server URL from: " + jdbcUrl, e);
        }
    }

    /**
     * 提取数据库名称
     */
    private String extractDatabaseName(String jdbcUrl) {
        try {
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("jdbc:mysql://[^/]+:(\\d+)/([^?]+)");
            java.util.regex.Matcher matcher = pattern.matcher(jdbcUrl);

            if (matcher.find()) {
                return matcher.group(2).trim();
            }
        } catch (Exception e) {
            logger.error("解析数据库名称失败: {}", jdbcUrl, e);
        }
        return null;
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        public String dbType;
        public boolean serverConnectionValid = false;
        public boolean databaseExists = false;
        public boolean httpScriptValid = false;
        
        public boolean isFullyValid() {
            return serverConnectionValid && httpScriptValid;
        }
        
        public boolean needsDatabaseCreation() {
            return serverConnectionValid && !databaseExists;
        }
    }
}
