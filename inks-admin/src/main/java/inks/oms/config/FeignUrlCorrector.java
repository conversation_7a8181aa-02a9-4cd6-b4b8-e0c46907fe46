package inks.oms.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Feign URL修正器
 * 拦截器会对目标 URL 的 host 为以下值的请求进行修正：
 * 例如system：
 * 原 URL 示例：http://system
 * 修正后：http://*************:10681/system
 */
@Configuration
public class FeignUrlCorrector {
    private static final Logger log = LoggerFactory.getLogger(FeignUrlCorrector.class);
    private static final Set<String> SERVICE_NAMES = new HashSet<>(Arrays.asList(
            "system", "auth", "utils", "qms", "file", "goods", "manu", "store", "job", "ems"));

    @Value("${inks.svcfeign:http://*************:10681}")
    private String baseUrl;

    @Bean
    public RequestInterceptor urlCorrectingInterceptor() {
        // 确保baseUrl末尾没有斜杠
        final String normalizedBaseUrl = baseUrl.endsWith("/") ?
                baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;

        log.info("初始化Feign URL修正拦截器, 基础URL: {}", normalizedBaseUrl);

        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                String targetUrl = template.feignTarget().url();
                String fullPath = template.path();

                try {
                    URI uri = new URI(targetUrl);
                    String host = uri.getHost();

                    // 检查是否是需要修正的服务名
                    if (SERVICE_NAMES.contains(host)) {
                        // 构建新的完整URL
                        String newBaseUrl = normalizedBaseUrl + "/" + host;
                        log.info("修正Feign请求URL基础部分: {} -> {}", targetUrl, newBaseUrl);

                        // 创建新的请求目标
                        template.target(newBaseUrl);

                        // 记录完整的请求URL用于调试
                        log.info("完整请求URL将为: {}{}", newBaseUrl, fullPath);
                    }
                } catch (Exception e) {
                    log.error("URL修正失败: {}", e.getMessage(), e);
                }
            }
        };
    }
}