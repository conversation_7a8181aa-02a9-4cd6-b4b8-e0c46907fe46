package inks.oms.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;

/**
 * 内存监控配置
 * 提供内存使用情况的实时监控和告警
 */
@Configuration
public class MemoryMonitorConfig {

    /**
     * 自定义内存健康检查
     */
    @Bean
    public HealthIndicator memoryHealthIndicator() {
        return new MemoryHealthIndicator();
    }

    /**
     * 内存健康检查实现
     */
    public static class MemoryHealthIndicator implements HealthIndicator {
        private static final Logger logger = LoggerFactory.getLogger(MemoryHealthIndicator.class);

        @Override
        public Health health() {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
            MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();

            long heapUsed = heapUsage.getUsed();
            long heapMax = heapUsage.getMax();
            long nonHeapUsed = nonHeapUsage.getUsed();
            long nonHeapMax = nonHeapUsage.getMax();

            // 计算使用率
            double heapUsagePercent = (double) heapUsed / heapMax * 100;
            double nonHeapUsagePercent = nonHeapMax > 0 ? (double) nonHeapUsed / nonHeapMax * 100 : 0;

            Health.Builder builder = new Health.Builder();

            // 内存使用率超过80%时标记为DOWN
            if (heapUsagePercent > 80 || nonHeapUsagePercent > 80) {
                builder.down()
                        .withDetail("warning", "内存使用率过高");
            } else {
                builder.up();
            }

            return builder
                    .withDetail("heap.used", formatBytes(heapUsed))
                    .withDetail("heap.max", formatBytes(heapMax))
                    .withDetail("heap.usage.percent", String.format("%.2f%%", heapUsagePercent))
                    .withDetail("nonHeap.used", formatBytes(nonHeapUsed))
                    .withDetail("nonHeap.max", formatBytes(nonHeapMax))
                    .withDetail("nonHeap.usage.percent", String.format("%.2f%%", nonHeapUsagePercent))
                    .build();
        }

        private String formatBytes(long bytes) {
            if (bytes < 1024) return bytes + " B";
            if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
            if (bytes < 1024 * 1024 * 1024) return String.format("%.2f MB", bytes / (1024.0 * 1024));
            return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
        }
    }

    /**
     * 定时内存监控任务
     */
    @Component
    public static class MemoryMonitorTask {
        private static final Logger logger = LoggerFactory.getLogger(MemoryMonitorTask.class);

        @Scheduled(fixedRate = 60000) // 每分钟执行一次
        public void monitorMemory() {
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
            MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();

            long heapUsed = heapUsage.getUsed();
            long heapMax = heapUsage.getMax();
            double heapUsagePercent = (double) heapUsed / heapMax * 100;

            // 记录内存使用情况
            logger.info("内存监控 - 堆内存使用: {}/{} ({:.2f}%), 非堆内存使用: {}/{}", 
                    formatBytes(heapUsed), 
                    formatBytes(heapMax), 
                    heapUsagePercent,
                    formatBytes(nonHeapUsage.getUsed()),
                    formatBytes(nonHeapUsage.getMax()));

            // 内存使用率超过70%时发出警告
            if (heapUsagePercent > 70) {
                logger.warn("⚠️ 内存使用率告警: 堆内存使用率 {:.2f}% 超过70%阈值", heapUsagePercent);
            }

            // 内存使用率超过85%时发出严重警告
            if (heapUsagePercent > 85) {
                logger.error("🚨 内存使用率严重告警: 堆内存使用率 {:.2f}% 超过85%阈值，建议立即检查", heapUsagePercent);
            }
        }

        private String formatBytes(long bytes) {
            if (bytes < 1024) return bytes + " B";
            if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
            if (bytes < 1024 * 1024 * 1024) return String.format("%.2f MB", bytes / (1024.0 * 1024));
            return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
        }
    }
}
